<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Datatables extends MY_Model
{
    protected $class;
    protected $methodclass;
    protected $ColumnsSearch;
    private $COUNT_DATA;
    private $COUNT_FILTERED;
    public $draw;
    public $length;
    public $start;
    public $number;
    public $method;
    public $search;
    public $param;
    public $orderField;
    public $orderType;
    public $arg;

    private $_throwable = false;

    public function make($class, $methodclass, $ColumnsSearch)
    {
        $this->class = $class;
        $this->methodclass = $methodclass;
        $this->ColumnsSearch = $ColumnsSearch;
        $this->load->model($class);
        $this->init();

        return $this;
    }

    public function init()
    {
        $class = $this->class;
        $methodclass = $this->methodclass;
        $ColumnsSearch = $this->ColumnsSearch;

        if (!method_exists($this->$class, $methodclass)) {
            if ($this->_throwable) {
                throw new Exception("Undefined method <b>$methodclass</b> in Model Class <b><i>$class</i></b>");
            }

            echo "Undefined method <b>$methodclass</b> in Model Class <b><i>$class</i></b>";
            exit;
        }

        if (!property_exists($this->$class, $ColumnsSearch)) {
            if ($this->_throwable) {
                throw new Exception("Undefined variable <b>$ColumnsSearch</b> in Model Class <b><i>$class</i></b>");
            }

            echo "Undefined variable <b>$ColumnsSearch</b> in Model Class <b><i>$class</i></b>";
            exit;
        }

        $method = $this->input->method();

        $this->method = $method;
        $this->draw = $this->input->$method('draw');
        $this->length = $this->input->$method('length');
        $this->start = $this->input->$method('start');
        $this->number = $this->start + 1;
    }

    private function getColumnsSearch($data)
    {
        if ($this->db->dbdriver == "mysqli") {
            return "TRIM(LOWER($data))";
        } else if ($this->db->dbdriver == "postgre") {
            return "TRIM(LOWER($data::text))";
        } else {
            if ($this->_throwable) {
                throw new Exception("This database driver not supported in this function");
            }

            echo "This database driver not supported in this function";
            exit;
        }
    }

    private function setParam($param)
    {
        $this->param = $param;
        $this->setCountData();
        $this->setCountFiltered();
    }

    private function processParam()
    {
        if ($this->param && count($this->param) > 0) {
            foreach ($this->param as $field => $value) {
                if (is_array($value)) {
                    if (count($value) > 0) {
                        $this->db->group_start();

                        foreach ($value as $fieldOr => $valueOr) {
                            if (is_array($valueOr)) {
                                foreach ($valueOr as $valueOr2) {
                                    if (is_null($valueOr2)) {
                                        $this->db->or_where($fieldOr);
                                    } else {
                                        $this->db->or_where($fieldOr, $valueOr2);
                                    }
                                }
                            } else {
                                if (is_null($valueOr)) {
                                    $this->db->or_where($fieldOr);
                                } else {
                                    $this->db->or_where($fieldOr, $valueOr);
                                }
                            }
                        }

                        $this->db->group_end();
                    }
                } else {
                    if (is_array($value)) {
                        foreach ($value as $valueAnd) {
                            if (is_null($valueAnd)) {
                                $this->db->where($field);
                            } else {
                                $this->db->where($field, $valueAnd);
                            }
                        }
                    } else {
                        if (is_null($value)) {
                            $this->db->where($field);
                        } else {
                            $this->db->where($field, $value);
                        }
                    }
                }
            }
        }

        if ($this->arg) {
            $arg = $this->arg;

            if (isset($arg['order'])) {
                $order = $arg['order'];

                if (isset($order['column']['name']) && isset($order['column']['value'])) {
                    $this->orderField = $order['column']['name'];
                    $this->orderType = $order['column']['value'];

                    $this->db->order_by($order['column']['name'], $order['column']['value']);
                }
            }
        }
    }

    public function getNumber()
    {
        return $this->number++;
    }

    private function setOtherArguments($arg)
    {
        $this->arg = $arg;
    }

    private function instanceQuery()
    {
        $class = $this->class;
        $methodclass = $this->methodclass;
        $this->$class->$methodclass();
    }

    public function searchData()
    {
        $method = $this->method;
        $class = $this->class;
        $search = isset($this->input->$method('search')['value']) ? $this->input->$method('search')['value'] : '';

        $ColumnsSearch = $this->ColumnsSearch;

        if (!empty($search)) {
            $index = 0;
            $this->search = strtolower(trim($search));

            if (count($this->$class->$ColumnsSearch) > 0) {
                $this->db->group_start();

                foreach ($this->$class->$ColumnsSearch as $column) {
                    if (!empty($column)) {
                        $exp = explode(",", $column);

                        if (count($exp) > 1) {
                            foreach ($exp as $field) {
                                $SearchFiled = $this->getColumnsSearch($field);

                                ($index === 0) ? $this->db->like($SearchFiled, $this->search) : $this->db->or_like($SearchFiled, $this->search);
                                $index++;
                            }
                        } else {
                            $SearchFiled = $this->getColumnsSearch($column);

                            ($index === 0) ? $this->db->like($SearchFiled, $this->search) : $this->db->or_like($SearchFiled, $this->search);
                            $index++;
                        }
                    }
                }

                $this->db->group_end();
            }
        }

        $order = $this->input->$method('order');

        if (!empty($order)) {
            if (isset($this->$class->$ColumnsSearch[$order['0']['column']])) {
                $OrderField = $this->$class->$ColumnsSearch[$order['0']['column']];
                $OrderType = $order['0']['dir'];

                $this->orderField = $OrderField;
                $this->orderType = $OrderType;

                if (!empty($OrderField)) {
                    $exp = explode(",", $OrderField);

                    if (count($exp) > 1) {
                        foreach ($exp as $value) {
                            $this->db->order_by(trim($value), $OrderType);
                        }
                    } else {
                        $this->db->order_by(trim($OrderField), $OrderType);
                    }
                }
            }
        }
    }

    public function getData($param = array(), $arg = array())
    {
        $this->setParam($param);

        if (count($arg) > 0) {
            $this->setOtherArguments($arg);
        }

        $this->instanceQuery();
        $this->searchData();
        $this->processParam();

        if ($this->length != -1) {
            $this->db->limit($this->length, $this->start);
        }

        return $this->db->get()->result();
    }

    public function setCountData()
    {
        $this->instanceQuery();
        $this->processParam();

        $this->COUNT_DATA = $this->db->count_all_results() ?? 0;
    }

    public function setCountFiltered()
    {
        $this->instanceQuery();
        $this->searchData();
        $this->processParam();

        $this->COUNT_FILTERED = $this->db->count_all_results() ?? 0;
    }

    public function getCountData()
    {
        return $this->COUNT_DATA ?? 0;
    }

    public function getCountFiltered()
    {
        return $this->COUNT_FILTERED ?? 0;
    }

    public function json($data)
    {
        $output = array(
            "draw" => $this->draw,
            "recordsTotal" => $this->getCountData(),
            "recordsFiltered" => $this->getCountFiltered(),
            "data" => $data
        );

        echo json_encode($output);
    }
}
