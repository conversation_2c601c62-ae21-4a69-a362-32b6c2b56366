<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Penugasan</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Penugasan</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Penugasan</h4>
                </div>

                <div>
                    <?php if ((isVillages() && $type == 'desa') || (isPMD() && $type == 'pmd') || (isKecamatan() && $type == 'kecamatan')): ?>
                        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah Penugasan</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <?php if (isPMD() || isKecamatan()): ?>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="startdate" class="form-label">Tanggal Mulai (Penugasan)</label>
                                <input type="date" name="startdate" id="startdate" class="form-control" value="<?= $startdate ?? null ?>">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="enddate" class="form-label">Tanggal Selesai (Penugasan)</label>
                                <input type="date" name="enddate" id="enddate" class="form-control" value="<?= $enddate ?? null ?>">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="createdby" class="form-label">Dibuat Oleh</label>
                                <select name="createdby" id="createdby" class="form-control">
                                    <option value="">- Pilih -</option>
                                    <option value="DPMD">Dinas DPMD</option>
                                    <option value="Kecamatan">Kecamatan</option>
                                    <option value="Villages">Desa</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="taskfor" class="form-label">Ditugaskan Kepada</label>
                                <select name="taskfor" id="taskfor" class="form-control">
                                    <option value="">- Pilih -</option>
                                    <option value="BPD">BPD</option>
                                    <option value="Operator Desa">Operator Desa</option>
                                </select>
                            </div>
                        </div>

                        <?php if (isPMD()): ?>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="kecamatanid" class="form-label">Kecamatan</label>
                                    <select name="kecamatanid" id="kecamatanid" class="form-control">
                                        <option value="">- Pilih -</option>
                                        <?php foreach ($kecamatan as $key => $value): ?>
                                            <option value="<?= $value->kecamatanid ?>"><?= $value->kecamatanname ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="villageid" class="form-label">Desa</label>
                                <select name="villageid" id="villageid" class="form-control">
                                    <option value="">- Pilih -</option>
                                    <?php if (isKecamatan()): ?>
                                        <?php foreach ($village as $key => $value): ?>
                                            <option value="<?= $value->id ?>"><?= $value->desaname ?></option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="actions" class="form-label">Aksi</label>

                                <div>
                                    <button type="button" class="btn btn-primary" onclick="filterData(true)">
                                        <i class="fa fa-search"></i>
                                        <span>Cari</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped datatables-task">
                        <thead>
                            <tr>
                                <th>Ditugaskan Kepada</th>
                                <th>Deadline</th>
                                <th>Kabupaten/Kota</th>
                                <th>Kecamatan</th>
                                <th>Desa</th>
                                <th>Awal Penugasan</th>
                                <th>Tugas</th>
                                <th>Keterangan</th>
                                <th>Status</th>
                                <th>Dibuat Oleh</th>
                                <th>Verifikator</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function releaseTask(id) {
        return swal({
            title: 'Apakah Anda yakin?',
            text: 'Tugas yang sudah di release tidak dapat diubah lagi!',
            icon: 'warning',
            buttons: true,
            dangerMode: true,
        }).then(willRelease => {
            if (willRelease) {
                return $.ajax({
                    url: '<?= base_url(uri_string() . '/release') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return swalMessageSuccess(response.MESSAGE, ok => {
                                return window.location.reload();
                            });
                        } else {
                            return swalMessageFailed(response.MESSAGE);
                        }
                    },
                    error: function() {
                        return swalError();
                    }
                });
            }
        });
    }

    function verifyTask(id) {
        return swal({
            title: 'Apakah Anda yakin?',
            text: 'Tugas yang sudah diverifikasi tidak dapat diubah lagi!',
            icon: 'warning',
            buttons: true,
            dangerMode: true,
        }).then(willVerify => {
            if (willVerify) {
                return $.ajax({
                    url: '<?= base_url(uri_string() . '/verify') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return swalMessageSuccess(response.MESSAGE, ok => {
                                return window.location.reload();
                            });
                        } else {
                            return swalMessageFailed(response.MESSAGE);
                        }
                    },
                    error: function() {
                        return swalError();
                    }
                });
            }
        });
    }

    function rejectTask(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/reject') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    }

    function collectTask(id) {
        $.ajax({
            url: '<?= base_url('penugasan/collect') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    }

    function historyTask(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/history') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    }

    function processTask(id) {
        return swal({
            title: 'Apakah Anda yakin?',
            text: 'Tugas yang sudah diproses tidak dapat diubah lagi!',
            icon: 'warning',
            buttons: true,
            dangerMode: true,
        }).then(willProcess => {
            if (willProcess) {
                return $.ajax({
                    url: '<?= base_url(uri_string() . '/process') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return swalMessageSuccess(response.MESSAGE, ok => {
                                return window.location.reload();
                            });
                        } else {
                            return swalMessageFailed(response.MESSAGE);
                        }
                    },
                    error: function() {
                        return swalError();
                    }
                });
            }
        });
    }

    function switchTask(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/switch') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
        }).fail(function() {
            return swalError();
        });
    }

    function detailStatus(id, status) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/detail_status') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id,
                status: status
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
        }).fail(function() {
            return swalError();
        });
    }

    window.onload = function() {
        <?php if (isPMD() || isKecamatan()): ?>
            $('#kecamatanid').on('change', function() {
                let kecamatanid = $(this).val();
                let desaid = <?= json_encode($village); ?>;

                let desa = desaid.filter(function(item) {
                    return item.kecamatanid == kecamatanid;
                });

                let html = '<option value="">- Pilih -</option>';

                desa.forEach(function(item) {
                    html += '<option value="' + item.id + '">' + item.desaname + '</option>';
                });

                $('#villageid').html(html);
            });

            $('#kecamatanid').trigger('change');
        <?php endif; ?>

        filterData();
    };

    function filterData(destroy = false) {
        let startdate = $('#startdate').val();
        let enddate = $('#enddate').val();
        let createdby = $('#createdby').val();
        let taskfor = $('#taskfor').val();
        let kecamatanid = $('#kecamatanid').val();
        let villageid = $('#villageid').val();

        if (destroy) {
            $('.datatables-task').DataTable().destroy();
            $('.datatables-task').parent().html(`<table class="table table-striped datatables-task">
                        <thead>
                            <tr>
                                <th>Ditugaskan Kepada</th>
                                <th>Deadline</th>
                                <th>Kabupaten/Kota</th>
                                <th>Kecamatan</th>
                                <th>Desa</th>
                                <th>Awal Penugasan</th>
                                <th>Tugas</th>
                                <th>Keterangan</th>
                                <th>Status</th>
                                <th>Dibuat Oleh</th>
                                <th>Verifikator</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>`);

            $('.datatables-task').DataTable({
                ordering: false,
                responsive: true,
                serverSide: true,
                processing: true,
                ajax: {
                    url: '<?= base_url(uri_string() . '/datatables') ?>',
                    method: 'POST',
                    data: {
                        startdate: startdate,
                        enddate: enddate,
                        createdby: createdby,
                        taskfor: taskfor,
                        kecamatanid: kecamatanid,
                        villageid: villageid,
                    },
                    error: function(xhr, error, thrown) {
                        console.log('AJAX Error:', error, thrown);
                        console.log('Response:', xhr.responseText);
                    }
                },
                language: {
                    emptyTable: "No data available in table",
                    zeroRecords: "No matching records found",
                    processing: "Loading..."
                },
                initComplete: function() {
                    console.log('DataTable initialized');
                },
                drawCallback: function() {
                    console.log('DataTable draw completed');
                }
            });
        } else {
            $('.datatables-task').DataTable({
                ordering: false,
                responsive: true,
                serverSide: true,
                processing: true,
                ajax: {
                    url: '<?= base_url(uri_string() . '/datatables') ?>',
                    method: 'POST',
                    data: {
                        startdate: startdate,
                        enddate: enddate,
                        createdby: createdby,
                        taskfor: taskfor,
                        kecamatanid: kecamatanid,
                        villageid: villageid,
                    },
                    error: function(xhr, error, thrown) {
                        console.log('AJAX Error:', error, thrown);
                        console.log('Response:', xhr.responseText);
                    }
                },
                language: {
                    emptyTable: "No data available in table",
                    zeroRecords: "No matching records found",
                    processing: "Loading..."
                },
                initComplete: function() {
                    console.log('DataTable initialized');
                },
                drawCallback: function() {
                    console.log('DataTable draw completed');
                }
            });
        }
    }
</script>